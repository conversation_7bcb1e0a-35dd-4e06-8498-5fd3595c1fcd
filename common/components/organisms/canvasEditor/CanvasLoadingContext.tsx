'use client'

import React, { 
  createContext, useContext, useState, ReactNode,
} from 'react';

interface LoadingStates {
  isGenerating: boolean;
  isVectorizing: boolean;
  isImproving: boolean;
  isSaving: boolean;
  isRemovingBackground: boolean;
}

interface CanvasLoadingContextType {
  loadingStates: LoadingStates;
  setGenerating: (loading: boolean) => void;
  setVectorizing: (loading: boolean) => void;
  setImproving: (loading: boolean) => void;
  setSaving: (loading: boolean) => void;
  setRemovingBackground: (loading: boolean) => void;
}

const CanvasLoadingContext = createContext<CanvasLoadingContextType | undefined>(undefined);

interface CanvasLoadingProviderProps {
  children: ReactNode;
}

export const CanvasLoadingProvider = ({ children }: CanvasLoadingProviderProps) => {
  const [loadingStates, setLoadingStates] = useState<LoadingStates>({
    isGenerating: false,
    isVectorizing: false,
    isImproving: false,
    isSaving: false,
  });

  const setGenerating = (loading: boolean) => {
    setLoadingStates(prev => ({ 
      ...prev, 
      isGenerating: loading,
    }));
  };

  const setVectorizing = (loading: boolean) => {
    setLoadingStates(prev => ({ 
      ...prev, 
      isVectorizing: loading,
    }));
  };

  const setImproving = (loading: boolean) => {
    setLoadingStates(prev => ({ 
      ...prev, 
      isImproving: loading,
    }));
  };

  const setSaving = (loading: boolean) => {
    setLoadingStates(prev => ({ 
      ...prev, 
      isSaving: loading,
    }));
  };

  return (
    <CanvasLoadingContext.Provider
      value={{
        loadingStates,
        setGenerating,
        setVectorizing,
        setImproving,
        setSaving,
      }}
    >
      {children}
    </CanvasLoadingContext.Provider>
  );
};

export const useCanvasLoading = () => {
  const context = useContext(CanvasLoadingContext);
  if (context === undefined) {
    throw new Error('useCanvasLoading must be used within a CanvasLoadingProvider');
  }
  return context;
};
